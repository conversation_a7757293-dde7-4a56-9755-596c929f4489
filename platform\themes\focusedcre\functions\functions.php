<?php

use <PERSON><PERSON>qi\Blog\Models\Post;
use Shaqi\Page\Models\Page;
use Shaqi\Team\Models\Team;
use Shaqi\Base\Facades\Form;
use Shaqi\Base\Facades\Html;
use Shaqi\Menu\Facades\Menu;
use Shaqi\Theme\Facades\Theme;
// use Shaqi\Base\Forms\FormHelper;
use Shaqi\Base\Facades\MetaBox;
use Shaqi\Media\Facades\RvMedia;
use Shaqi\Base\Forms\FormAbstract;
use Shaqi\Base\Enums\BaseStatusEnum;
use Shaqi\Ecommerce\Models\Customer;
use Shaqi\Ecommerce\Models\Product;
use Illuminate\Support\Facades\Route;
use Illuminate\Database\Eloquent\Model;
use Kris\LaravelFormBuilder\FormHelper;
use Shaqi\Menu\Models\Menu as MenuModel;
use Shaqi\Testimonial\Models\Testimonial;
use Theme\Focusedcre\Fields\ThemeIconField;
use Shaqi\SocialLogin\Facades\SocialService;
use Shaqi\FeaturedWebsite\Models\FeaturedWebsite;
use Shaqi\Faq\Repositories\Interfaces\FaqInterface;
use <PERSON>haqi\Page\Repositories\Interfaces\PageInterface;
use Shaqi\Team\Repositories\Interfaces\TeamInterface;
use Shaqi\Portfolio\Repositories\Interfaces\PortfolioInterface;
use Shaqi\Testimonial\Repositories\Interfaces\TestimonialInterface;
use Shaqi\FeaturedBlock\Repositories\Interfaces\FeaturedBlockInterface;
use Shaqi\Contact\Repositories\Interfaces\NewWebsiteInterface;
use Shaqi\Contact\Repositories\Interfaces\RedesignWebsiteInterface;

register_page_template([
    'default' => __('Default'),
    'homepage' => __('Home Page'),
    'page-detail' => __('Page detail'),
    'about' => __('About Template'),
    'why-partner' => __('Why Partner Template'),
    'team' => __('Team Page'),
    'blog' => __('Blog Page'),
    'website-development' => __('Website Development'),
    'featured-websites' => __('Featured Websites'),
    'marketing-solutions' => __('Marketing Solutions'),
    'single-solution' => __('Single Solution'),
    'portfolio' => __('Portfolio'),
    'single-portfolio' => __('Single Portfolio'),
    'single-portfolio-dev' => __('Single Portfolio Dev'),
    'cre-marketplace' => __('CRE Marketplace'),
    'single-marketplace' => __('Single Marketplace'),
    'blog-package' => __('Blog Package'),
    'ebook' => __('eBook'),
    'faqs' => __('Faqs'),
    'careers' => __('Careers'),
    'seo-custom-template' => __('SEO Custom Template'),
    'testimonials' => __('Testimonials'),
    'contact' => __('Contact Us'),
    'employee-login' => __('Employee Login'),
    'broker-portal' => __('Broker Portal'),
    'listing-presentation-request' => __('Listing Presentation Request (Broker Portal)'),
    'om-request' => __('Offering Memorandum Request (Broker Portal)'),
    'flyer-request' => __('Flyer Request (Broker Portal)'),
    'sitemap' => __('Sitemap Page'),
    'privacy-policy' => __('Privacy Policy'),
    'seo' => __('Seo'),
    'canva-om' => __('Canva OM Templates'),
    'how-it-works' => __('How It Works Template'),
    'cre-website-templates' => __('CRE Website Templates'),
    'custom-property-database-plugin' => __('Custom Property Database Plugin'),
    'portfolio-dev' => __('Portfolio Dev'),
    'redesign-my-website' => __('Redesign Website Template'),
    'make-my-website' => __('Create New Website Template'),
     'social-media' => __('Social Media'),
]);

register_sidebar([
    'id' => 'pre_footer_sidebar',
    'name' => __('Pre footer sidebar'),
    'description' => __('Widgets at the bottom of the page.'),
]);

register_sidebar([
    'id' => 'footer_sidebar',
    'name' => __('Footer sidebar'),
    'description' => __('Widgets in footer of page'),
]);

if (is_plugin_active('ecommerce')) {
    register_sidebar([
        'id' => 'product_list_sidebar',
        'name' => __('Product list sidebar'),
        'description' => __('Widgets in product list page'),
    ]);

    register_sidebar([
        'id' => 'product_list_bottom_sidebar',
        'name' => __('Product list bottom sidebar'),
        'description' => __('Widgets in product list bottom page'),
    ]);
}

Menu::addMenuLocation('header-menu', __('Header Navigation'));
Menu::addMenuLocation('footer-bottom-menu', __('Footer Bottom Menu'));

RvMedia::setUploadPathAndURLToPublic();
RvMedia::addSize('large', 620, 380)
    ->addSize('medium', 398, 255)
    ->addSize('small', 300, 280)
    ->addSize('portfolio-thumb', 460, 0);

Form::component('themeIcon', Theme::getThemeNamespace('partials.icons-field'), [
    'name',
    'value' => null,
    'attributes' => [],
]);

add_filter('form_custom_fields', function (FormAbstract $form, FormHelper $formHelper) {
    if (!$formHelper->hasCustomField('themeIcon')) {
        $form->addCustomField('themeIcon', ThemeIconField::class);
    }

    return $form;
}, 29, 2);



add_action([BASE_ACTION_AFTER_CREATE_CONTENT, BASE_ACTION_AFTER_UPDATE_CONTENT], function ($type, $request, $object) {


    if (get_class($object) === Page::class) {
        if ($request->has('header_image')) {
            MetaBox::saveMetaBoxData($object, 'header_image', $request->input('header_image'));
        }
    }
}, 75, 3);

add_filter(BASE_FILTER_BEFORE_GET_FRONT_PAGE_ITEM, function ($data, $model) {
    if (get_class($model) == MenuModel::class) {
        $data->with([
            'metadata',
            'menuNodes.child.metadata',
        ]);
    }

    return $data;
}, 3, 2);

if (is_plugin_active('social-login')) {
    app()->booted(function () {
        if (defined('SOCIAL_LOGIN_MODULE_SCREEN_NAME') && Route::has('customer.login')) {
            SocialService::registerModule([
                'guard' => 'customer',
                'model' => Customer::class,
                'login_url' => route('customer.login'),
                'redirect_url' => route('public.index'),
                'view' => Theme::getThemeNamespace('partials.ecommerce.social-login-options'),
                'use_css' => false,
            ]);
        }
    });
}

add_filter(BASE_FILTER_BEFORE_RENDER_FORM, function (FormAbstract $form, ?Model $data) {
    if (get_class($data) == Page::class) {
        $form
            ->addAfter('image', 'header_image', 'mediaImage', [
                'label' => __('Header image'),
                'label_attr' => ['class' => 'control-label'],
                'value' => MetaBox::getMetaData($data, 'header_image', true),
            ]);
    }

    return $form;
}, 120, 3);

add_filter(BASE_FILTER_BEFORE_RENDER_FORM, function ($form, $data) {
    if (get_class($data) === Page::class) {

        $solution_category = MetaBox::getMetaData($data, 'solution_category', true);

        $form->addAfter('template', 'solution_category', 'customSelect', [
            'label'      => __('Solution Category'),
            'label_attr' => ['class' => 'control-label'],
            'attr'       => [
                'class'            => 'form-control select2',
            ],
            'selected'   => $solution_category,
            'choices'    => [
                '' => __('Select Category'),
                'property-marketing' => __('Property Marketing'),
                'brand-identity' => __('Brand & Identity'),
                'digital-solutions' => __('Digital Solutions'),
                'campaign-outreach' => __('Campaign & Outreach'),
            ],
        ]);


        $form->modify('description', 'editor', [
            'label'      => __('Banner Content'),
            'label_attr' => ['class' => 'control-label'],
            'attr'       => [
                'rows' => 2,
                'placeholder' => trans('core/base::forms.description_placeholder'),
                'data-counter' => 400,
                'with-short-code' => false, // if true, it will add a button to select shortcode
                'without-buttons' => true, // if true, all buttons will be hidden
            ],
        ]);

        if ($data->template == 'seo-custom-template') {
            $short_text = MetaBox::getMetaData($data, 'short_text', true);
            $url = MetaBox::getMetaData($data, 'url', true);
            $featured_box_1 = MetaBox::getMetaData($data, 'featured_box_1', true);
            $featured_box_2 = MetaBox::getMetaData($data, 'featured_box_2', true);
            $featured_box_3 = MetaBox::getMetaData($data, 'featured_box_3', true);
            $form->addAfter('description', 'short_text', 'editor', [
                'label'      => __('Short Text'),
                'label_attr' => ['class' => 'control-label'],
                'value'      => $short_text,
                'attr'       => [
                    'rows' => 2,
                    'placeholder' => __('Seo custom template short text'),
                    'with-short-code' => false, // if true, it will add a button to select shortcode
                    'without-buttons' => true, // if true, all buttons will be hidden
                ]
            ])->addAfter('short_text', 'url', 'text', [
                'label'      => __('Button link'),
                'label_attr' => ['class' => 'control-label'],
                'value'      => $url,
                'attr'       => [
                    'placeholder' => __('/website-development'),
                ]
            ])->addAfter('url', 'rowOne1', 'html', [
                'html' => '<div class="row">',
            ])->addAfter('rowOne1', 'featured_box_1', 'customSelect', [
                'label'      => __('Featured Block'),
                'label_attr' => ['class' => 'control-label required'],
                'choices' => ['' => 'Select'] + app(FeaturedBlockInterface::class)->pluck('name', 'id'),
                'selected' => $featured_box_1,
                'wrapper' => [
                    'class' => 'form-group col-md-4'
                ]
            ])->addAfter('featured_box_1', 'featured_box_2', 'customSelect', [
                'label'      => __('Featured Block'),
                'label_attr' => ['class' => 'control-label required'],
                'choices' => ['' => 'Select'] + app(FeaturedBlockInterface::class)->pluck('name', 'id'),
                'selected' => $featured_box_2,
                'wrapper' => [
                    'class' => 'form-group col-md-4'
                ]
            ])->addAfter('featured_box_2', 'featured_box_3', 'customSelect', [
                'label'      => __('Featured Block'),
                'label_attr' => ['class' => 'control-label required'],
                'choices' => ['' => 'Select'] + app(FeaturedBlockInterface::class)->pluck('name', 'id'),
                'selected' => $featured_box_3,
                'wrapper' => [
                    'class' => 'form-group col-md-4'
                ]
            ])->addAfter('featured_box_3', 'rowClose1', 'html', [
                'html' => '</div>',
            ]);
        }
        return $form;
    }
}, 130, 3);

    add_action(BASE_ACTION_AFTER_CREATE_CONTENT, 'save_addition_fields_pages', 120, 3);
    add_action(BASE_ACTION_AFTER_UPDATE_CONTENT, 'save_addition_fields_pages', 120, 3);

    function save_addition_fields_pages($screen, $request, $data)
    {

        if (get_class($data) == Page::class) {

            MetaBox::saveMetaBoxData($data, 'solution_category', $request->input('solution_category'));


            if ($data->template == 'seo-custom-template') {
            MetaBox::saveMetaBoxData($data, 'short_text', $request->input('short_text'));
            MetaBox::saveMetaBoxData($data, 'url', $request->input('url'));

            MetaBox::saveMetaBoxData($data, 'featured_box_1', $request->input('featured_box_1'));
            MetaBox::saveMetaBoxData($data, 'featured_box_2', $request->input('featured_box_2'));
            MetaBox::saveMetaBoxData($data, 'featured_box_3', $request->input('featured_box_3'));

            }

        }
    }

if (is_plugin_active('team')) {
    add_filter(BASE_FILTER_BEFORE_RENDER_FORM, function ($form, $data) {
        if (get_class($data) == Team::class) {

            $designation = MetaBox::getMetaData($data, 'designation', true);
            $linkedin = MetaBox::getMetaData($data, 'linkedin', true);
            $city_state = MetaBox::getMetaData($data, 'city_state', true);
            $phone = MetaBox::getMetaData($data, 'phone', true);
            $email = MetaBox::getMetaData($data, 'email', true);
            $time = MetaBox::getMetaData($data, 'time', true);
            $form
                ->addAfter('name', 'rowOne1', 'html', [
                    'html' => '<div class="row">',
                ])
                ->addAfter('rowOne1', 'designation', 'text', [
                    'label'      => __('Designation'),
                    'label_attr' => ['class' => 'control-label'],
                    'value'      => $designation,
                    'attr'       => [
                        'placeholder' => __('Designation'),
                    ],
                    'wrapper' => [
                        'class' => 'form-group col-md-4'
                    ]

                ])
                ->addAfter('designation', 'phone', 'text', [
                    'label'      => __('Phone'),
                    'label_attr' => ['class' => 'control-label'],
                    'value'      => $phone,
                    'attr'       => [
                        'placeholder' => __('Phone'),
                    ],
                    'wrapper' => [
                        'class' => 'form-group col-md-4'
                    ]

                ])
                ->addAfter('phone', 'email', 'text', [
                    'label'      => __('Email'),
                    'label_attr' => ['class' => 'control-label'],
                    'value'      => $email,
                    'attr'       => [
                        'placeholder' => __('<EMAIL>'),
                    ],
                    'wrapper' => [
                        'class' => 'form-group col-md-4'
                    ]

                ])
                ->addAfter('email', 'linkedin', 'text', [
                    'label'      => __('Linkedin'),
                    'label_attr' => ['class' => 'control-label'],
                    'value'      => $linkedin,
                    'attr'       => [
                        'placeholder' => __('https://www.linkedin.com/in/your-name-xyz/'),
                    ],
                    'wrapper' => [
                        'class' => 'form-group col-md-4'
                    ]

                ])
                ->addAfter('linkedin', 'city_state', 'text', [
                    'label'      => __('City State'),
                    'label_attr' => ['class' => 'control-label'],
                    'value'      => $city_state,
                    'attr'       => [
                        'placeholder' => __('Enter city state'),
                    ],
                    'wrapper' => [
                        'class' => 'form-group col-md-4'
                    ]

                ])
                ->addAfter('city_state', 'time', 'text', [
                    'label'      => __('Time'),
                    'label_attr' => ['class' => 'control-label'],
                    'value'      => $time,
                    'attr'       => [
                        'placeholder' => __('EST'),
                    ],
                    'wrapper' => [
                        'class' => 'form-group col-md-4'
                    ]

                ])
                ->addAfter('time', 'rowClose1', 'html', [
                    'html' => '</div>',
                ]);
        }

        return $form;
    }, 120, 3);

    add_action(BASE_ACTION_AFTER_CREATE_CONTENT, 'save_addition_fields', 120, 3);
    add_action(BASE_ACTION_AFTER_UPDATE_CONTENT, 'save_addition_fields', 120, 3);

    function save_addition_fields($screen, $request, $data)
    {

        if (get_class($data) == Team::class) {
            MetaBox::saveMetaBoxData($data, 'designation', $request->input('designation'));
            MetaBox::saveMetaBoxData($data, 'linkedin', $request->input('linkedin'));
            MetaBox::saveMetaBoxData($data, 'city_state', $request->input('city_state'));
            MetaBox::saveMetaBoxData($data, 'phone', $request->input('phone'));
            MetaBox::saveMetaBoxData($data, 'email', $request->input('email'));
            MetaBox::saveMetaBoxData($data, 'time', $request->input('time'));

            // dd($sMB);
        }
    }
}

if (is_plugin_active('testimonial')) {
    add_filter(BASE_FILTER_BEFORE_RENDER_FORM, function ($form, $data) {
        if (get_class($data) == Testimonial::class) {

            $company_logo = MetaBox::getMetaData($data, 'company_logo', true);


            $form
                ->addAfter('image', 'company_logo', 'mediaImage', [
                    'label'      => __('Company Logo'),
                    'label_attr' => ['class' => 'control-label required'],
                    'value'      => $company_logo,
                ]);
        }

        return $form;
    }, 120, 3);

    add_action(BASE_ACTION_AFTER_CREATE_CONTENT, 'save_addition_fields_testimonial', 120, 3);
    add_action(BASE_ACTION_AFTER_UPDATE_CONTENT, 'save_addition_fields_testimonial', 120, 3);

    function save_addition_fields_testimonial($screen, $request, $data)
    {

        if (get_class($data) == Testimonial::class) {
            MetaBox::saveMetaBoxData($data, 'company_logo', $request->input('company_logo'));
        }
    }
}

if (is_plugin_active('ecommerce')) {
    add_filter(BASE_FILTER_BEFORE_RENDER_FORM, function ($form, $data) {
        if (get_class($data) == Product::class) {

            $product_sub_title = MetaBox::getMetaData($data, 'product_sub_title', true);
            $live_demo = MetaBox::getMetaData($data, 'live_demo', true);


            $form
                ->addAfter('name', 'product_sub_title', 'text', [
                    'label'      => __('Product Sub Title'),
                    'label_attr' => ['class' => 'control-label required'],
                    'value'      => $product_sub_title,
                ])
                ->addAfter('content', 'live_demo', 'text', [
                    'label'      => __('Live Demo'),
                    'label_attr' => ['class' => 'control-label'],
                    'value'      => $live_demo,
                ]);
        }

        return $form;
    }, 120, 3);

    add_action(BASE_ACTION_AFTER_CREATE_CONTENT, 'save_addition_fields_product', 120, 3);
    add_action(BASE_ACTION_AFTER_UPDATE_CONTENT, 'save_addition_fields_product', 120, 3);

    function save_addition_fields_product($screen, $request, $data)
    {

        if (get_class($data) == Product::class) {
            MetaBox::saveMetaBoxData($data, 'product_sub_title', $request->input('product_sub_title'));
            MetaBox::saveMetaBoxData($data, 'live_demo', $request->input('live_demo'));
        }
    }
}

if (is_plugin_active('featured-website')) {
    add_filter(BASE_FILTER_BEFORE_RENDER_FORM, function ($form, $data) {
        if (get_class($data) == FeaturedWebsite::class) {

            $website_logo = MetaBox::getMetaData($data, 'website_logo', true);
            $website_tag = MetaBox::getMetaData($data, 'website_tag', true);
            $website_button_link = MetaBox::getMetaData($data, 'website_button_link', true);

            $form
                ->addAfter('name', 'rowOne1', 'html', [
                    'html' => '<div class="row">',
                ])
                ->addAfter('image', 'website_logo', 'mediaImage', [
                    'label'      => __('Website Logo'),
                    'label_attr' => ['class' => 'control-label'],
                    'value'      => $website_logo,
                    'wrapper' => [
                        'class' => 'form-group col-md-12'
                    ]

                ])
                ->addAfter('rowOne1', 'website_tag', 'customSelect', [
                    'label'      => __('Website Tag'),
                    'label_attr' => ['class' => 'control-label'],
                    'attr'          => [
                        'class'            => 'form-control select2',
                    ],
                    'value'      => $website_tag,
                    'choices'    => [
                        'Landing Page' => __('Landing Page'),
                        'The Vitals' => __('The Vitals'),
                        'Optimal' => __('Optimal'),
                        'Property Website' => __('Property Website'),
                    ],
                    'wrapper' => [
                        'class' => 'form-group col-md-6'
                    ]

                ])
                ->addAfter('website_tag', 'website_button_link', 'text', [
                    'label'      => __('Website Link'),
                    'label_attr' => ['class' => 'control-label'],
                    'value'      => $website_button_link,
                    'attr'       => [
                        'placeholder' => __('https://example.com'),
                    ],
                    'wrapper' => [
                        'class' => 'form-group col-md-6'
                    ]

                ])

                ->addAfter('website_button_link', 'rowClose1', 'html', [
                    'html' => '</div>',
                ]);
        }

        return $form;
    }, 120, 3);

    add_action(BASE_ACTION_AFTER_CREATE_CONTENT, 'save_addition_fields_featured_website', 120, 3);
    add_action(BASE_ACTION_AFTER_UPDATE_CONTENT, 'save_addition_fields_featured_website', 120, 3);

    function save_addition_fields_featured_website($screen, $request, $data)
    {

        if (get_class($data) == FeaturedWebsite::class) {
            MetaBox::saveMetaBoxData($data, 'website_logo', $request->input('website_logo'));
            MetaBox::saveMetaBoxData($data, 'website_tag', $request->input('website_tag'));
            MetaBox::saveMetaBoxData($data, 'website_button_link', $request->input('website_button_link'));


            // dd($sMB);
        }
    }
}


if (!function_exists('get_all_testimonials')) {
    function get_all_testimonials()
    {

        $condition = ['status' => BaseStatusEnum::PUBLISHED];
        $testimonials = app(TestimonialInterface::class)->advancedGet([
            'condition' => $condition,
            'order_by' => ['created_at' => 'desc']
        ]);

        return $testimonials;
    }
}
if (!function_exists('get_all_teams')) {
    function get_all_teams()
    {
        return Team::orderBy('order', 'asc')->get();
    }
}
if (!function_exists('get_all_featured_websites')) {
    function get_all_featured_websites()
    {
        return FeaturedWebsite::with('categories')->orderBy('order', 'asc')->get();
    }
}

if (!function_exists('get_portfolio_by_category_ids')) {
    function get_portfolio_by_category_ids($category_ids)
    {

        $condition = ['status' => BaseStatusEnum::PUBLISHED];

        if ($category_ids) {
            $categoryIds = explode(',', $category_ids);

            if ($categoryIds) {
                $condition[] = ['category_id', 'IN', $categoryIds];
            }
        }

        $portfolios = app(PortfolioInterface::class)->advancedGet([
            'condition' => $condition,
            'order_by' => ['order' => 'asc']
        ]);

        return $portfolios;
        //return FeaturedWebsite::orderBy('order', 'asc')->get();
    }
}
if (!function_exists('get_marketing_solutions')) {
    function get_marketing_solutions()
    {

$condition = ['status' => BaseStatusEnum::PUBLISHED];
        $condition[] = ['template', '=', 'single-solution'];

        $pages = app(PageInterface::class)->advancedGet([
            'condition' => $condition,
            'order_by' => ['order' => 'asc']
        ]);

        return $pages;
    }
}

if (!function_exists('get_portfolio_pages')) {
    function get_portfolio_pages()
    {

        return get_page_by_template('single-portfolio');
    }
}

if (!function_exists('get_page_by_template')) {
    function get_page_by_template($template)
    {

        $condition = ['status' => BaseStatusEnum::PUBLISHED];
        $condition[] = ['template', '=', $template];

        $portfolio_pages = app(PageInterface::class)->advancedGet([
            'condition' => $condition,
            'order_by' => ['created_at' => 'asc']
        ]);

        return $portfolio_pages;
    }
}

if (!function_exists('get_all_faqs')) {
    function get_all_faqs()
    {
        $condition = ['status' => BaseStatusEnum::PUBLISHED];
        $faqs = app(FaqInterface::class)->advancedGet([
            'condition' => $condition,
        ]);

        return $faqs;
    }
}

if (!function_exists('get_marketing_solutions_by_category')) {
    function get_marketing_solutions_by_category($category = null)
    {
        $condition = ['status' => BaseStatusEnum::PUBLISHED];
        $condition[] = ['template', '=', 'single-solution'];

        $pages = app(PageInterface::class)->advancedGet([
            'condition' => $condition,
            'order_by' => ['order' => 'asc']
        ]);

        if ($category) {
            $filteredPages = collect();

            foreach ($pages as $page) {
                $solutionCategory = MetaBox::getMetaData($page, 'solution_category', true);

                if ($solutionCategory == $category) {
                    $filteredPages->push($page);
                }
            }

            return $filteredPages;
        }

        return $pages;
    }
}

if (!function_exists('get_new_website_by_token')) {
    function get_new_website_by_token($token)
    {
        return app(NewWebsiteInterface::class)->getFirstBy(['save_token' => $token]);
    }
}

if (!function_exists('get_redesign_website_by_token')) {
    function get_redesign_website_by_token($token)
    {
        return app(RedesignWebsiteInterface::class)->getFirstBy(['save_token' => $token]);
    }
}
