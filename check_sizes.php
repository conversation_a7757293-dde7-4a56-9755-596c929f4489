<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Shaqi\Media\Facades\RvMedia;

$sizes = RvMedia::getSizes();
echo "Current thumbnail sizes:\n";
echo json_encode($sizes, JSON_PRETTY_PRINT) . "\n";

echo "\nChecking if portfolio-thumb size exists:\n";
if (array_key_exists('portfolio-thumb', $sizes)) {
    echo "✓ portfolio-thumb size found: " . $sizes['portfolio-thumb'] . "\n";
} else {
    echo "✗ portfolio-thumb size NOT found\n";
}
